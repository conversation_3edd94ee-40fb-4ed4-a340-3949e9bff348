@echo off
setlocal enabledelayedexpansion

echo ========================================
echo EITS Windows Portable Build Script
echo ========================================
echo.
echo Bu script EİTS uygulamasını portable hale getirir.
echo Sonuç dosyalar kurulum gerektirmez ve doğrudan <PERSON>ı<PERSON>.
echo.

:: MSVC toolchain kontrolü
rustup show | findstr "msvc" >nul
if errorlevel 1 (
    echo WARNING: MSVC toolchain not detected
    echo Setting MSVC as default toolchain...
    rustup default stable-x86_64-pc-windows-msvc
)

echo Installing dependencies...
call pnpm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Building frontend...
call npm run build
if errorlevel 1 (
    echo ERROR: Frontend build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Building Portable Version...
echo ========================================

:: No-bundle build için exe dosyası oluştur
echo Building executable only...
cd src-tauri
cargo tauri build --no-bundle --config tauri.nobundle.conf.json
if errorlevel 1 (
    echo ERROR: Portable build failed
    pause
    exit /b 1
)

:: Portable klasörü oluştur
set "PORTABLE_DIR=..\EITS-Portable"
if exist "%PORTABLE_DIR%" rmdir /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

:: Exe dosyasını kopyala
copy "target\release\EITS-Portable.exe" "%PORTABLE_DIR%\" >nul
if not exist "%PORTABLE_DIR%\EITS-Portable.exe" (
    echo ERROR: Could not find executable file
    pause
    exit /b 1
)

:: WebView2 runtime'ı kopyala (eğer varsa)
if exist "Microsoft.WebView2.FixedVersionRuntime.137.0.3296.68.x64" (
    echo Copying WebView2 runtime...
    xcopy /E /I /Q "Microsoft.WebView2.FixedVersionRuntime.137.0.3296.68.x64" "%PORTABLE_DIR%\WebView2" >nul
)

:: Database ve config dosyalarını kopyala
if exist "database.sqlite" (
    copy "database.sqlite" "%PORTABLE_DIR%\" >nul
)

:: README dosyası oluştur
echo Creating portable README...
(
echo EİTS - Elektronik İmza Takip Sistemi ^(Portable^)
echo ================================================
echo.
echo Bu portable versiyondur - kurulum gerektirmez.
echo.
echo Çalıştırmak için:
echo 1. EITS-Portable.exe dosyasını çift tıklayın
echo 2. Eğer WebView2 hatası alırsanız, Microsoft Edge'i güncelleyin
echo.
echo Not: Bu versiyon ayarları kendi klasöründe saklar.
echo.
echo Versiyon: 0.1.0
echo Oluşturulma Tarihi: %date% %time%
) > "%PORTABLE_DIR%\README.txt"

cd ..

echo.
echo ========================================
echo Portable Build Completed!
echo ========================================
echo.
echo Portable files created in: %PORTABLE_DIR%
echo Main executable: %PORTABLE_DIR%\EITS-Portable.exe
echo.
echo Dosya boyutu:
for %%I in ("%PORTABLE_DIR%\EITS-Portable.exe") do echo Executable: %%~zI bytes

echo.
echo Portable klasörünü istediğiniz yere kopyalayabilirsiniz.
echo.
pause
