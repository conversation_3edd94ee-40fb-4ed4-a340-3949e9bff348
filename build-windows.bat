@echo off
echo ========================================
echo EITS Windows Build Script
echo ========================================
echo.

REM Check if pnpm is installed
where pnpm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: pnpm is not installed or not in PATH
    echo Please install pnpm: npm install -g pnpm
    pause
    exit /b 1
)

REM Check if Rust is installed
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Rust/Cargo is not installed or not in PATH
    echo Please install Rust from: https://rustup.rs/
    pause
    exit /b 1
)

REM Check if we're using MSVC toolchain
rustup show | findstr "stable-msvc" >nul
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: MSVC toolchain not detected
    echo Setting MSVC as default toolchain...
    rustup default stable-msvc
)

echo Installing dependencies...
pnpm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Building frontend...
pnpm run build
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Frontend build failed
    pause
    exit /b 1
)

echo.
echo Building Windows application with offline WebView2 installer...
echo This will create a larger installer (~127MB) but works without internet.
pnpm run build:windows-offline
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Windows build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Installers can be found in:
echo   src-tauri\target\release\bundle\msi\
echo   src-tauri\target\release\bundle\nsis\
echo.
pause
