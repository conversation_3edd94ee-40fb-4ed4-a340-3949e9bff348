@echo off
echo ========================================
echo EITS Windows Offline Build Script
echo ========================================
echo.
echo This script builds EITS with offline WebView2 installer.
echo The resulting installer will be larger (~127MB) but will
echo work without internet connection during installation.
echo.

REM Check if pnpm is installed
where pnpm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: pnpm is not installed or not in PATH
    echo Please install pnpm: npm install -g pnpm
    pause
    exit /b 1
)

REM Check if Rust is installed
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Rust/Cargo is not installed or not in PATH
    echo Please install Rust from: https://rustup.rs/
    pause
    exit /b 1
)

REM Check if we're using MSVC toolchain
rustup show | findstr "stable-msvc" >nul
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: MSVC toolchain not detected
    echo Setting MSVC as default toolchain...
    rustup default stable-msvc
)

echo Installing dependencies...
pnpm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Building frontend...
pnpm run build
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Frontend build failed
    pause
    exit /b 1
)

echo.
echo Building Windows application with offline WebView2...
echo This may take longer due to the larger WebView2 runtime...
pnpm run build:windows-offline
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Windows offline build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Offline Build completed successfully!
echo ========================================
echo.
echo Features of this build:
echo   ✓ No internet required during installation
echo   ✓ Consistent WebView2 version across all installations
echo   ✓ Better reliability for enterprise environments
echo   ✓ Larger installer size (~127MB additional)
echo.
echo Installers can be found in:
echo   src-tauri\target\release\bundle\msi\
echo   src-tauri\target\release\bundle\nsis\
echo.
pause
