{"$schema": "https://schema.tauri.app/config/2", "productName": "EITS", "version": "0.1.0", "identifier": "com.eits.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "EİTS - Elektronik İmza Takip Sistemi", "width": 1200, "height": 800, "maximized": false, "decorations": false, "transparent": false, "titleBarStyle": "Overlay", "minWidth": 800, "minHeight": 600, "center": true, "resizable": true, "fullscreen": false, "theme": "Light", "visible": true, "skipTaskbar": false}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "tooltip": "EİTS - Elektronik İmza Takip Sistemi"}}, "bundle": {"active": true, "targets": ["msi", "nsis"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.ico"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "webviewInstallMode": {"type": "offlineInstaller"}, "wix": {"language": ["en-US", "tr-TR"], "template": "main.wxs"}, "nsis": {"displayLanguageSelector": true, "installMode": "perMachine"}}}}