{"$schema": "https://schema.tauri.app/config/2", "productName": "EITS-Portable", "version": "0.1.0", "identifier": "com.eits.app.portable", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "EİTS - Elektronik İmza Takip Sistemi (Portable)", "width": 1200, "height": 800, "maximized": true, "decorations": false, "transparent": false, "titleBarStyle": "Overlay", "minWidth": 800, "minHeight": 600, "center": true, "resizable": true, "fullscreen": false, "theme": "Light"}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "tooltip": "EİTS - Elektronik İmza Takip Sistemi (Portable)"}}, "bundle": {"active": false}}