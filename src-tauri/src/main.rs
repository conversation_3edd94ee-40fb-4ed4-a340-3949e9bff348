// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::panic;

fn main() {
    // Set up panic handler for better error reporting
    panic::set_hook(Box::new(|panic_info| {
        eprintln!("Application panicked: {}", panic_info);

        // Try to write to a log file as well
        if let Ok(mut file) = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open("eits_crash.log") {
            use std::io::Write;
            let _ = writeln!(file, "[{}] Application panicked: {}",
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"),
                panic_info);
        }
    }));

    eits_lib::run()
}
