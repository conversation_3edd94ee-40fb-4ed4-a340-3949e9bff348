import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import {
  Cog6ToothIcon,
  ClockIcon,
  BellIcon,
  ShieldCheckIcon,
  DocumentArrowDownIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ServerIcon
} from '@heroicons/react/24/outline';
import { useSettingsStore, defaultSettings } from '@/store/settingsStore';
import {
  getAllSettings,
  convertSettingsToSystemSettings,
  convertSystemSettingsToSettings,
  upsertSetting
} from '@/lib/tauri-api';


// System Settings Schema
const systemSettingsSchema = z.object({
  // General Settings
  systemName: z.string().min(1, 'Sistem adı gereklidir'),
  organizationName: z.string().min(1, 'Kurum adı gereklidir'),
  systemLanguage: z.enum(['tr', 'en'], { required_error: 'Dil seçimi yapınız' }),
  timeZone: z.string().min(1, 'Saat dilimi seçiniz'),
  dateFormat: z.enum(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'], { required_error: 'Tarih formatı seçiniz' }),
  
  // Signature Settings
  signatureToleranceMinutes: z.number().min(0).max(120, 'En fazla 120 dakika olabilir'),
  autoViolationDetection: z.boolean(),
  requireReasonForLateSignature: z.boolean(),
  maxDailySignatures: z.number().min(1).max(10, 'En fazla 10 olabilir'),
  
  // Session & Security Settings
  sessionTimeoutMinutes: z.number().min(5).max(480, '5-480 dakika arası olmalıdır'),
  passwordMinLength: z.number().min(4).max(50, '4-50 karakter arası olmalıdır'),
  requireStrongPassword: z.boolean(),
  enableTwoFactorAuth: z.boolean(),
  maxLoginAttempts: z.number().min(3).max(10, '3-10 deneme arası olmalıdır'),
  lockoutDurationMinutes: z.number().min(5).max(1440, '5-1440 dakika arası olmalıdır'),
  
  // Notification Settings
  enableEmailNotifications: z.boolean(),
  emailServer: z.string().optional(),
  emailPort: z.number().optional(),
  emailUsername: z.string().optional(),
  emailPassword: z.string().optional(),
  enableSystemNotifications: z.boolean(),
  notificationSound: z.boolean(),
  
  // Data Management
  autoBackupEnabled: z.boolean(),
  backupIntervalDays: z.number().min(1).max(30, '1-30 gün arası olmalıdır'),
  dataRetentionDays: z.number().min(30).max(3650, '30-3650 gün arası olmalıdır'),
  
  // Display Settings
  recordsPerPage: z.number().min(10).max(100, '10-100 kayıt arası olmalıdır'),
  enableAnimations: z.boolean(),
  compactMode: z.boolean(),
  showWelcomeMessage: z.boolean(),
});

type SystemSettingsFormData = z.infer<typeof systemSettingsSchema>;

export default function SystemSettingsPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [isInitialized, setIsInitialized] = useState(false);
  const { setSettings } = useSettingsStore();

  const form = useForm<SystemSettingsFormData>({
    resolver: zodResolver(systemSettingsSchema),
    defaultValues: defaultSettings,
  });

  // Load settings from database on component mount
  useEffect(() => {
    if (isInitialized) return;

    const loadSettings = async () => {
      try {
        setIsLoading(true);
        const dbSettings = await getAllSettings();
        const systemSettings = convertSettingsToSystemSettings(dbSettings);

        // Merge with defaults to ensure all fields are present
        const mergedSettings = { ...defaultSettings, ...systemSettings };

        // Only update form, don't update store here to avoid loops
        form.reset(mergedSettings);
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to load settings:', error);
        toast({
          title: 'Hata',
          description: 'Ayarlar yüklenirken bir hata oluştu.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [isInitialized, form, toast]);

  const handleSubmit = async (data: SystemSettingsFormData) => {
    setIsLoading(true);
    try {
      console.log('Saving system settings:', data);

      // Convert form data to settings format
      const settingsToSave = convertSystemSettingsToSettings(data);

      // Save each setting to the database
      for (const setting of settingsToSave) {
        await upsertSetting(setting);
      }

      // Update local store
      setSettings(data);

      toast({
        title: 'Başarılı',
        description: 'Sistem ayarları kaydedildi.',
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast({
        title: 'Hata',
        description: 'Ayarlar kaydedilirken bir hata oluştu.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetDefaults = async () => {
    if (confirm('Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
      setIsLoading(true);
      try {
        // Reset form to defaults
        form.reset(defaultSettings);

        // Save defaults to database
        const settingsToSave = convertSystemSettingsToSettings(defaultSettings);
        for (const setting of settingsToSave) {
          await upsertSetting(setting);
        }

        // Update local store
        setSettings(defaultSettings);

        toast({
          title: 'Bilgi',
          description: 'Ayarlar varsayılan değerlere sıfırlandı.',
        });
      } catch (error) {
        console.error('Failed to reset settings:', error);
        toast({
          title: 'Hata',
          description: 'Ayarlar sıfırlanırken bir hata oluştu.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleBackupNow = async () => {
    setIsLoading(true);
    try {
      // Simulate backup process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'Başarılı',
        description: 'Sistem yedeği başarıyla oluşturuldu.',
      });
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Yedekleme işlemi başarısız oldu.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  const tabs = [
    { id: 'general', label: 'Genel Ayarlar', icon: Cog6ToothIcon },
    { id: 'signature', label: 'İmza Ayarları', icon: ClockIcon },
    { id: 'security', label: 'Güvenlik Ayarları', icon: ShieldCheckIcon },
    { id: 'notifications', label: 'Bildirim Ayarları', icon: BellIcon },
    { id: 'data', label: 'Veri Yönetimi', icon: ServerIcon },
    { id: 'display', label: 'Görünüm Ayarları', icon: Cog6ToothIcon },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
              <Cog6ToothIcon className="w-8 h-8 text-blue-600" />
              Sistem Ayarları
            </h1>
            <p className="text-gray-600">
              Sistem genelinde geçerli olan yapılandırma ayarlarını burada yönetebilirsiniz.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="px-4 py-2 bg-green-50 border-green-200 text-green-700">
              <CheckCircleIcon className="w-4 h-4 mr-2" />
              Sistem Aktif
            </Badge>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Sidebar Navigation */}
              <div className="lg:col-span-1">
                <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg sticky top-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-sm font-semibold text-gray-700">Ayar Kategorileri</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <nav className="space-y-1">
                      {tabs.map((tab) => {
                        const Icon = tab.icon;
                        return (
                          <button
                            key={tab.id}
                            type="button"
                            onClick={() => setActiveTab(tab.id)}
                            className={`w-full flex items-center gap-3 px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                              activeTab === tab.id
                                ? 'bg-blue-600 text-white shadow-md'
                                : 'text-gray-600 hover:bg-blue-50 hover:text-blue-600'
                            }`}
                          >
                            <Icon className="w-5 h-5" />
                            <span className="text-sm font-medium">{tab.label}</span>
                          </button>
                        );
                      })}
                    </nav>
                  </CardContent>
                </Card>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-3 space-y-6">
                {/* General Settings */}
                {activeTab === 'general' && (
                  <Card className="bg-gradient-to-br from-white via-slate-50/80 to-blue-50/30 backdrop-blur-sm border border-slate-200/50 shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-xl text-gray-800">
                        <Cog6ToothIcon className="w-6 h-6 text-blue-600" />
                        Genel Ayarlar
                      </CardTitle>
                      <CardDescription>
                        Sistem temel yapılandırma ayarları
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="systemName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Sistem Adı <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Sistem adını giriniz"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="organizationName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Kurum Adı <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Kurum adını giriniz"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="systemLanguage"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Sistem Dili <span className="text-red-500">*</span>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                                <FormControl>
                                  <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                                    <SelectValue placeholder="Dil seçiniz" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent className="border border-slate-200 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg">
                                  <SelectItem value="tr" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    🇹🇷 Türkçe
                                  </SelectItem>
                                  <SelectItem value="en" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    🇺🇸 English
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="timeZone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Saat Dilimi <span className="text-red-500">*</span>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                                <FormControl>
                                  <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                                    <SelectValue placeholder="Saat dilimi seçiniz" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent className="border border-slate-200 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg">
                                  <SelectItem value="Europe/Istanbul" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    Türkiye (UTC+3)
                                  </SelectItem>
                                  <SelectItem value="UTC" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    UTC (UTC+0)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="dateFormat"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Tarih Formatı <span className="text-red-500">*</span>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                                <FormControl>
                                  <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                                    <SelectValue placeholder="Tarih formatı seçiniz" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent className="border border-slate-200 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg">
                                  <SelectItem value="DD/MM/YYYY" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    DD/MM/YYYY (31/12/2024)
                                  </SelectItem>
                                  <SelectItem value="MM/DD/YYYY" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    MM/DD/YYYY (12/31/2024)
                                  </SelectItem>
                                  <SelectItem value="YYYY-MM-DD" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                                    YYYY-MM-DD (2024-12-31)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Signature Settings */}
                {activeTab === 'signature' && (
                  <Card className="bg-gradient-to-br from-white via-slate-50/80 to-orange-50/30 backdrop-blur-sm border border-slate-200/50 shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-xl text-gray-800">
                        <ClockIcon className="w-6 h-6 text-orange-600" />
                        İmza Ayarları
                      </CardTitle>
                      <CardDescription>
                        İmza periyotları ve tolerans süreleri yapılandırması
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="signatureToleranceMinutes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                İmza Tolerans Süresi (Dakika)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  max="120"
                                  placeholder="15"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-orange-500/30 focus:border-orange-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                />
                              </FormControl>
                              <FormMessage />
                              <p className="text-xs text-slate-500 mt-1">
                                İmza saatlerinde kabul edilebilir gecikme süresi
                              </p>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="maxDailySignatures"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Günlük Maksimum İmza Sayısı
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="10"
                                  placeholder="5"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-orange-500/30 focus:border-orange-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                />
                              </FormControl>
                              <FormMessage />
                              <p className="text-xs text-slate-500 mt-1">
                                Bir hükümlünün günde atabileceği maksimum imza sayısı
                              </p>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="autoViolationDetection"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Otomatik İhlal Tespiti
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Kaçırılan imzalar otomatik olarak ihlal olarak işaretlensin
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-orange-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="requireReasonForLateSignature"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Geç İmza için Neden Gerekli
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Tolerans süresini aşan imzalar için neden belirtilmesi zorunlu olsun
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-orange-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Security Settings */}
                {activeTab === 'security' && (
                  <Card className="bg-gradient-to-br from-white via-slate-50/80 to-red-50/30 backdrop-blur-sm border border-slate-200/50 shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-xl text-gray-800">
                        <ShieldCheckIcon className="w-6 h-6 text-red-600" />
                        Güvenlik Ayarları
                      </CardTitle>
                      <CardDescription>
                        Oturum süresi ve şifre politikaları
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="sessionTimeoutMinutes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Oturum Zaman Aşımı (Dakika)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="5"
                                  max="480"
                                  placeholder="480"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-red-500/30 focus:border-red-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 5)}
                                />
                              </FormControl>
                              <FormMessage />
                              <p className="text-xs text-slate-500 mt-1">
                                Kullanıcı hareketsizlik durumunda oturum kapatma süresi
                              </p>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="passwordMinLength"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Minimum Şifre Uzunluğu
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="4"
                                  max="50"
                                  placeholder="6"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-red-500/30 focus:border-red-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 4)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="maxLoginAttempts"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Maksimum Giriş Denemesi
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="3"
                                  max="10"
                                  placeholder="3"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-red-500/30 focus:border-red-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 3)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="lockoutDurationMinutes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Hesap Kilitleme Süresi (Dakika)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="5"
                                  max="1440"
                                  placeholder="15"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-red-500/30 focus:border-red-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 5)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="requireStrongPassword"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Güçlü Şifre Gerekli
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Büyük harf, küçük harf, rakam ve özel karakter zorunlu olsun
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-red-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="enableTwoFactorAuth"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  İki Faktörlü Doğrulama
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Giriş işlemlerinde ek doğrulama adımı gerekli olsun
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-red-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Notification Settings */}
                {activeTab === 'notifications' && (
                  <Card className="bg-gradient-to-br from-white via-slate-50/80 to-purple-50/30 backdrop-blur-sm border border-slate-200/50 shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-xl text-gray-800">
                        <BellIcon className="w-6 h-6 text-purple-600" />
                        Bildirim Ayarları
                      </CardTitle>
                      <CardDescription>
                        E-posta ve sistem bildirimleri yapılandırması
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="enableSystemNotifications"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Sistem Bildirimleri
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Tarayıcı içi sistem bildirimlerini etkinleştir
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-purple-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="notificationSound"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Bildirim Sesi
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Sistem bildirimlerinde ses çalınsın
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-purple-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="enableEmailNotifications"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  E-posta Bildirimleri
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Önemli olaylar için e-posta gönderimi
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-purple-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {form.watch('enableEmailNotifications') && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 border-2 border-dashed border-purple-200 rounded-xl bg-purple-50/50">
                          <div className="md:col-span-2">
                            <h4 className="text-lg font-semibold text-gray-800 mb-2">E-posta Sunucu Ayarları</h4>
                            <p className="text-sm text-gray-600 mb-4">E-posta bildirimlerinin gönderilmesi için SMTP sunucu bilgilerini yapılandırın.</p>
                          </div>

                          <FormField
                            control={form.control}
                            name="emailServer"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-semibold text-slate-700">
                                  SMTP Sunucu
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="smtp.gmail.com"
                                    disabled={isLoading}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-purple-500/30 focus:border-purple-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="emailPort"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-semibold text-slate-700">
                                  Port
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="587"
                                    disabled={isLoading}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-purple-500/30 focus:border-purple-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="emailUsername"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-semibold text-slate-700">
                                  Kullanıcı Adı
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="<EMAIL>"
                                    disabled={isLoading}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-purple-500/30 focus:border-purple-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="emailPassword"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-semibold text-slate-700">
                                  Şifre
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="password"
                                    placeholder="••••••••"
                                    disabled={isLoading}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-purple-500/30 focus:border-purple-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Data Management */}
                {activeTab === 'data' && (
                  <Card className="bg-gradient-to-br from-white via-slate-50/80 to-green-50/30 backdrop-blur-sm border border-slate-200/50 shadow-lg">                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-xl text-gray-800">
                        <ServerIcon className="w-6 h-6 text-green-600" />
                        Veri Yönetimi
                      </CardTitle>
                      <CardDescription>
                        Veri yedekleme ve saklama politikaları
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="backupIntervalDays"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Otomatik Yedekleme Aralığı (Gün)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="30"
                                  placeholder="7"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-green-500/30 focus:border-green-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="dataRetentionDays"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Veri Saklama Süresi (Gün)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="30"
                                  max="3650"
                                  placeholder="1095"
                                  disabled={isLoading}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-green-500/30 focus:border-green-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 30)}
                                />
                              </FormControl>
                              <FormMessage />
                              <p className="text-xs text-slate-500 mt-1">
                                Bu süreden eski veriler otomatik olarak silinecektir
                              </p>
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="autoBackupEnabled"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base font-semibold text-slate-800">
                                Otomatik Yedekleme
                              </FormLabel>
                              <div className="text-sm text-slate-600">
                                Belirlenen aralıklarla otomatik sistem yedeği alınsın
                              </div>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={isLoading}
                                className="data-[state=checked]:bg-green-600"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <Separator />

                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                          <DocumentArrowDownIcon className="w-5 h-5 text-green-600" />
                          Manuel İşlemler
                        </h4>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleBackupNow}
                            disabled={isLoading}
                            className="flex items-center gap-2 px-6 py-3 bg-green-50 border-2 border-green-300 text-green-700 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-green-100 hover:border-green-400 hover:shadow-md focus:ring-2 focus:ring-green-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                          >
                            <DocumentArrowDownIcon className="w-4 h-4" />
                            {isLoading ? 'Yedekleniyor...' : 'Şimdi Yedekle'}
                          </Button>

                          <Button
                            type="button"
                            variant="outline"
                            disabled={isLoading}
                            className="flex items-center gap-2 px-6 py-3 bg-blue-50 border-2 border-blue-300 text-blue-700 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-blue-100 hover:border-blue-400 hover:shadow-md focus:ring-2 focus:ring-blue-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                          >
                            <ArrowPathIcon className="w-4 h-4" />
                            Yedekleri Görüntüle
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Display Settings */}
                {activeTab === 'display' && (
                  <Card className="bg-gradient-to-br from-white via-slate-50/80 to-indigo-50/30 backdrop-blur-sm border border-slate-200/50 shadow-lg">                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-xl text-gray-800">
                        <Cog6ToothIcon className="w-6 h-6 text-indigo-600" />
                        Görünüm Ayarları
                      </CardTitle>
                      <CardDescription>
                        Kullanıcı arayüzü ve görünüm seçenekleri
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="recordsPerPage"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">
                                Sayfa Başına Kayıt Sayısı
                              </FormLabel>
                              <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()} disabled={isLoading}>
                                <FormControl>
                                  <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-indigo-500/30 focus:border-indigo-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                                    <SelectValue placeholder="Kayıt sayısı seçiniz" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent className="border border-slate-200 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg">
                                  <SelectItem value="10" className="hover:bg-indigo-50 focus:bg-indigo-50 rounded-lg m-1">
                                    10 kayıt
                                  </SelectItem>
                                  <SelectItem value="25" className="hover:bg-indigo-50 focus:bg-indigo-50 rounded-lg m-1">
                                    25 kayıt
                                  </SelectItem>
                                  <SelectItem value="50" className="hover:bg-indigo-50 focus:bg-indigo-50 rounded-lg m-1">
                                    50 kayıt
                                  </SelectItem>
                                  <SelectItem value="100" className="hover:bg-indigo-50 focus:bg-indigo-50 rounded-lg m-1">
                                    100 kayıt
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="enableAnimations"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Animasyonları Etkinleştir
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Sayfa geçişlerinde ve etkileşimlerde animasyon efektleri
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-indigo-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="compactMode"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Kompakt Görünüm
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Daha sıkışık bir arayüz düzeni kullan (daha fazla bilgi)
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-indigo-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="showWelcomeMessage"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base font-semibold text-slate-800">
                                  Hoş Geldin Mesajı Göster
                                </FormLabel>
                                <div className="text-sm text-slate-600">
                                  Giriş yapıldığında ana sayfada karşılama mesajı gösterilsin
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-indigo-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Action Buttons */}
                <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleResetDefaults}
                          disabled={isLoading}
                          className="flex items-center gap-2 px-6 py-2.5 bg-white/80 border-2 border-slate-300 text-slate-600 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-slate-50 hover:border-slate-400 hover:shadow-md focus:ring-2 focus:ring-slate-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                          <ExclamationTriangleIcon className="w-4 h-4" />
                          Varsayılana Sıfırla
                        </Button>
                      </div>

                      <div className="flex items-center gap-4">
                        <Button
                          type="submit"
                          disabled={isLoading}
                          className="px-8 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl shadow-md hover:shadow-lg focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
                        >
                          {isLoading ? (
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                              Kaydediliyor...
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <CheckCircleIcon className="w-4 h-4" />
                              Ayarları Kaydet
                            </div>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
