import { useState, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'wouter';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { ArrowLeftIcon, PrinterIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link } from 'wouter';
import { getConvictWithDetails } from '../../lib/tauri-api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { formatDate } from '../../lib/utils';
import { generateSignatureDates, formatPeriodDisplay, groupSignaturePeriods } from '../../lib/signature-dates';
import type { SignaturePeriod } from '../../shared/schema';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';
import './SignatureForm.css';

export default function SignatureFormPage() {
  const { id } = useParams<{ id: string }>();
  const convictId = parseInt(id!, 10);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 24; // 24 signatures per page

  // PDF generation state
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Ref for the printable content
  const printContentRef = useRef<HTMLDivElement>(null);

  // Fetch convict details
  const { data: convict, isLoading, error } = useQuery({
    queryKey: ['convict-details', convictId],
    queryFn: () => getConvictWithDetails(convictId),
    enabled: !!convictId,
  });
  // Helper function to create a signature form page for a specific page of dates
  const createSignatureFormPage = (pageSignatureDates: string[], pageNumber: number) => {
    const pageElement = document.createElement('div');
    pageElement.className = 'signature-form-page pdf-export';
    
    // Apply consistent styling for PDF export
    pageElement.style.cssText = `
      width: 794px !important;
      min-height: 1123px !important;
      margin: 0 !important;
      padding: 20px !important;
      font-family: 'Inter', 'Arial', sans-serif !important;
      font-size: 12px !important;
      line-height: 1.4 !important;
      color: #000 !important;
      position: relative !important;
      background-color: #ffffff !important;
      box-sizing: border-box !important;
      transform-origin: top left !important;
    `;

    // Create the form structure with better layout control
    const titleDiv = document.createElement('div');
    titleDiv.style.cssText = `
      position: absolute !important;
      top: 0px !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      font-size: 14px !important;
      font-weight: 700 !important;
      text-align: center !important;
      letter-spacing: 1px !important;
      margin-bottom: 30px !important;
    `;
    titleDiv.textContent = 'YÜKÜMLÜ İMZA ÇİZELGESİ';

    const headerDiv = document.createElement('div');
    headerDiv.style.cssText = `
      display: flex !important;
      width: 100% !important;
      height: 280px !important;
      border: 1px solid #000 !important;
      margin-top: 40px !important;
      box-sizing: border-box !important;
    `;

    // Create header sections with proper structure
    const leftLabelDiv = document.createElement('div');
    leftLabelDiv.style.cssText = `
      width: 40px !important;
      background-color: #DCDDEA !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      border-right: 1px solid #000 !important;
      font-size: 12px !important;
      font-weight: 700 !important;
      text-align: center !important;
      position: relative !important;
    `;
    
    const leftLabelText = document.createElement('div');
    leftLabelText.style.cssText = `
      transform: rotate(90deg) !important;
      transform-origin: center !important;
      white-space: nowrap !important;
      font-size: 12px !important;
      font-weight: 700 !important;
    `;
    leftLabelText.textContent = 'Yükümlünün';
    leftLabelDiv.appendChild(leftLabelText);

    const fieldsDiv = document.createElement('div');
    fieldsDiv.style.cssText = `
      width: 180px !important;
      background-color: #DCDDEA !important;
      border-right: 1px solid #000 !important;
      display: flex !important;
      flex-direction: column !important;
    `;

    const valuesDiv = document.createElement('div');
    valuesDiv.style.cssText = `
      width: 180px !important;
      background-color: #ffffff !important;
      border-right: 1px solid #000 !important;
      display: flex !important;
      flex-direction: column !important;
    `;    const scheduleLabelDiv = document.createElement('div');
    scheduleLabelDiv.style.cssText = `
      width: 40px !important;
      background-color: #DCDDEA !important;
      border-right: 1px solid #000 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-size: 12px !important;
      font-weight: 700 !important;
      text-align: center !important;
      position: relative !important;
    `;
    
    const scheduleLabelText = document.createElement('div');
    scheduleLabelText.style.cssText = `
      transform: rotate(90deg) !important;
      transform-origin: center !important;
      white-space: nowrap !important;
      font-size: 12px !important;
      font-weight: 700 !important;
    `;
    scheduleLabelText.textContent = 'İmza Gün ve Saatleri';
    scheduleLabelDiv.appendChild(scheduleLabelText);    const scheduleDiv = document.createElement('div');
    scheduleDiv.style.cssText = `
      flex: 1 !important;
      background-color: #ffffff !important;
      border-right: 1px solid #000 !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      text-align: center !important;
      padding: 10px !important;
      font-size: 10px !important;
    `;    const sampleDiv = document.createElement('div');
    sampleDiv.style.cssText = `
      width: 130px !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: start !important;
      position: relative !important;
      background-color: #ffffff !important;
      padding: 10px !important;
      box-sizing: border-box !important;
      gap: 20px !important;
    `;// Create field rows
    const fields = [
      { label: 'Dosya Numarası', value: convict?.file_number || '-' },
      { label: 'Adı Soyadı', value: convict ? `${convict.first_name.toUpperCase()} ${convict.last_name.toUpperCase()}` : '-' },
      { label: 'T.C. Kimlik Numarası', value: convict?.tc_no || '-' },
      { label: 'Telefon Numarası', value: convict?.phone_number || '-' },
      { label: 'Yakınına Ait Telefon Numarası', value: convict?.relative_phone_number || '-' },
      { label: 'İkamet Adresi', value: convict?.address || '-' }
    ];

    fields.forEach((field, index) => {
      const fieldDiv = document.createElement('div');
      fieldDiv.style.cssText = `
        height: 46px !important;
        border-bottom: ${index < fields.length - 1 ? '1px solid #000' : 'none'} !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 12px !important;
        font-size: 11px !important;
        font-weight: 700 !important;
      `;
      fieldDiv.textContent = field.label;
      fieldsDiv.appendChild(fieldDiv);

      const valueDiv = document.createElement('div');
      valueDiv.style.cssText = `
        height: 46px !important;
        border-bottom: ${index < fields.length - 1 ? '1px solid #000' : 'none'} !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 12px !important;
        font-size: 11px !important;
        word-break: break-word !important;
      `;
      valueDiv.textContent = field.value;
      valuesDiv.appendChild(valueDiv);
    });

    // Add schedule information
    signaturePeriods.forEach(period => {
      const periodDiv = document.createElement('div');
      periodDiv.style.cssText = `
        margin: 2px 0 !important;
        font-size: 9px !important;
        line-height: 1.2 !important;
      `;
      periodDiv.innerHTML = `
        ${period.startDate} - ${period.endDate}<br>
        ${period.frequency}<br>
        ${period.days ? period.days + '<br>' : ''}
        ${period.time}
      `;
      scheduleDiv.appendChild(periodDiv);
    });    // Add signature sample box
    const sampleBox = document.createElement('div');
    sampleBox.style.cssText = `
      width: 100px !important;
      height: 100px !important;
      border: 1px solid #000 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-size: 40px !important;
      font-weight: 400 !important;
      margin: 0 auto !important;
      background-color: #ffffff !important;
      box-sizing: border-box !important;
      line-height: 1 !important;
    `;
    sampleBox.textContent = '1';const sampleLabel = document.createElement('div');
    sampleLabel.style.cssText = `
      font-size: 9px !important;
      font-weight: 600 !important;
      text-align: center !important;
      line-height: 1.1 !important;
      margin: 0 !important;
      width: 100% !important;
      letter-spacing: 0.2px !important;
      color: #000 !important;
    `;
    sampleLabel.innerHTML = 'YÜKÜMLÜNÜN<br>İMZA ÖRNEĞİ';

    sampleDiv.appendChild(sampleBox);
    sampleDiv.appendChild(sampleLabel);

    // Assemble header
    headerDiv.appendChild(leftLabelDiv);
    headerDiv.appendChild(fieldsDiv);
    headerDiv.appendChild(valuesDiv);
    headerDiv.appendChild(scheduleLabelDiv);
    headerDiv.appendChild(scheduleDiv);
    headerDiv.appendChild(sampleDiv);

    // Create table structure
    const tableDiv = document.createElement('div');
    tableDiv.style.cssText = `
      width: 100% !important;
      margin-top: 20px !important;
      border: 1px solid #000 !important;
      border-bottom: none !important;
    `;

    // Table headers
    const sectionHeaderDiv = document.createElement('div');
    sectionHeaderDiv.style.cssText = `
      display: flex !important;
      width: 100% !important;
      height: 30px !important;
      background-color: #DCDDEA !important;
    `;

    const convictHeaderDiv = document.createElement('div');
    convictHeaderDiv.style.cssText = `
      width: 50% !important;
      border-right: 1px solid #000 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-size: 13px !important;
      font-weight: 600 !important;
    `;
    convictHeaderDiv.textContent = 'YÜKÜMLÜNÜN';

    const officerHeaderDiv = document.createElement('div');
    officerHeaderDiv.style.cssText = `
      width: 50% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-size: 13px !important;
      font-weight: 600 !important;
    `;
    officerHeaderDiv.textContent = 'GÖREVLİNİN';

    sectionHeaderDiv.appendChild(convictHeaderDiv);
    sectionHeaderDiv.appendChild(officerHeaderDiv);

    const columnHeaderDiv = document.createElement('div');
    columnHeaderDiv.style.cssText = `
      display: flex !important;
      width: 100% !important;
      height: 30px !important;
      background-color: #DCDDEA !important;
      border-top: 1px solid #000 !important;
    `;    const columnHeaders = [
      { label: 'S.N.', width: '5%' },
      { label: 'İMZA TARİHİ', width: '13%' },
      { label: 'İMZA SAATİ', width: '10%' },
      { label: 'İMZASI', width: '22%' },
      { label: 'ADI SOYADI/ÜNVANI', width: '25%' },
      { label: 'İMZASI', width: '25%' }
    ];

    columnHeaders.forEach((header, index) => {
      const headerDiv = document.createElement('div');
      headerDiv.style.cssText = `
        width: ${header.width} !important;
        border-right: ${index < columnHeaders.length - 1 ? '1px solid #000' : 'none'} !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 11px !important;
        font-weight: 700 !important;
        text-align: center !important;
        padding: 0 4px !important;
      `;
      headerDiv.textContent = header.label;
      columnHeaderDiv.appendChild(headerDiv);
    });

    // Table rows
    const rowsDiv = document.createElement('div');
    rowsDiv.style.cssText = `
      width: 100% !important;
    `;

    pageSignatureDates.forEach((date, index) => {
      const rowDiv = document.createElement('div');
      rowDiv.style.cssText = `
        display: flex !important;
        width: 100% !important;
        height: 35px !important;
        border-top: 1px solid #000 !important;
      `;      const cellData = [
        { text: `${(pageNumber - 1) * itemsPerPage + index + 1}`, width: '5%' },
        { text: date, width: '13%' },
        { text: '', width: '10%' },
        { text: '', width: '22%' },
        { text: '', width: '25%' },
        { text: '', width: '25%' }
      ];

      cellData.forEach((cell, cellIndex) => {
        const cellDiv = document.createElement('div');
        cellDiv.style.cssText = `
          width: ${cell.width} !important;
          border-right: ${cellIndex < cellData.length - 1 ? '1px solid #000' : 'none'} !important;
          display: flex !important;
          align-items: center !important;
          justify-content: ${cellIndex === 0 ? 'center' : 'flex-start'} !important;
          font-size: 11px !important;
          padding: 0 8px !important;
        `;
        cellDiv.textContent = cell.text;
        rowDiv.appendChild(cellDiv);
      });

      rowsDiv.appendChild(rowDiv);
    });

    tableDiv.appendChild(sectionHeaderDiv);
    tableDiv.appendChild(columnHeaderDiv);
    tableDiv.appendChild(rowsDiv);

    // Assemble the complete page
    pageElement.appendChild(titleDiv);
    pageElement.appendChild(headerDiv);
    pageElement.appendChild(tableDiv);

    return pageElement;
  };
  const handlePrint = async () => {
    if (!convict) {
      return;
    }

    try {
      setIsGeneratingPDF(true);

      // Create PDF with proper dimensions
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        putOnlyUsedFonts: true,
        floatPrecision: 16 // Higher precision for better quality
      });

      // A4 dimensions in mm
      const pdfWidth = 210;
      const pdfHeight = 297;

      // Process all pages
      for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
        const startIndex = (pageNum - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageSignatureDates = allSignatureDates.slice(startIndex, endIndex);

        // Create page element
        const pageElement = createSignatureFormPage(pageSignatureDates, pageNum);

        // Add to DOM temporarily with a container for better rendering
        const container = document.createElement('div');
        container.style.cssText = `
          position: absolute !important;
          top: -10000px !important;
          left: -10000px !important;
          width: 794px !important;
          height: 1123px !important;
          background: white !important;
          z-index: -1000 !important;
          overflow: hidden !important;
        `;
        container.appendChild(pageElement);
        document.body.appendChild(container);

        // Wait for fonts and images to load
        await new Promise(resolve => setTimeout(resolve, 100));

        try {
          // Create canvas with optimized settings
          const canvas = await html2canvas(pageElement, {
            scale: 2, // Reduced scale for better performance while maintaining quality
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: 794,
            height: 1123,
            scrollX: 0,
            scrollY: 0,
            windowWidth: 794,            windowHeight: 1123,
            removeContainer: false,
            imageTimeout: 0,
            logging: false, // Disable logging for cleaner output
            onclone: (clonedDoc) => {
              // Ensure all styles are properly applied in the cloned document
              const clonedElement = clonedDoc.querySelector('.pdf-export') as HTMLElement;
              if (clonedElement) {
                clonedElement.style.transform = 'none';
                clonedElement.style.transformOrigin = 'top left';
              }
            }
          });

          // Remove from DOM
          document.body.removeChild(container);

          // Add page to PDF
          if (pageNum > 1) {
            pdf.addPage();
          }

          // Convert canvas to image and add to PDF
          const imgData = canvas.toDataURL('image/jpeg', 0.95); // Use JPEG with high quality
          
          // Calculate dimensions to maintain aspect ratio
          const canvasAspectRatio = canvas.width / canvas.height;
          const pdfAspectRatio = pdfWidth / pdfHeight;
          
          let imgWidth = pdfWidth;
          let imgHeight = pdfHeight;
          let offsetX = 0;
          let offsetY = 0;
          
          if (canvasAspectRatio > pdfAspectRatio) {
            // Canvas is wider, fit to width
            imgHeight = pdfWidth / canvasAspectRatio;
            offsetY = (pdfHeight - imgHeight) / 2;
          } else {
            // Canvas is taller, fit to height
            imgWidth = pdfHeight * canvasAspectRatio;
            offsetX = (pdfWidth - imgWidth) / 2;
          }

          pdf.addImage(imgData, 'JPEG', offsetX, offsetY, imgWidth, imgHeight);

        } catch (canvasError) {
          console.error('Canvas generation error for page', pageNum, ':', canvasError);
          // Remove container even if canvas generation fails
          if (document.body.contains(container)) {
            document.body.removeChild(container);
          }
          throw canvasError;
        }
      }

      // Generate filename
      const fileName = `İmza_Föyü_${convict.first_name}_${convict.last_name}_${formatDate(new Date()).replace(/\./g, '_')}.pdf`;

      // Show save dialog
      const filePath = await save({
        filters: [
          {
            name: 'PDF Dosyaları',
            extensions: ['pdf'],
          },
        ],
        defaultPath: fileName,
      });

      if (filePath) {
        // Save PDF
        const pdfArrayBuffer = pdf.output('arraybuffer');
        const pdfUint8Array = new Uint8Array(pdfArrayBuffer);
        
        await writeFile(filePath, pdfUint8Array);
        console.log('PDF başarıyla kaydedildi:', filePath);
      }

    } catch (error) {
      console.error('PDF oluşturma hatası:', error);
      alert('PDF oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (isLoading) {
    return (
      <div className="windows-content">
        <div className="flex flex-col items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error || !convict) {
    return (
      <div className="windows-content">
        <Card className="windows-card">
          <CardContent className="text-center py-8">
            <p className="text-red-600">Hükümlü bilgileri yüklenirken bir hata oluştu.</p>
            <Link href="/convicts">
              <Button variant="windows" className="mt-4">
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                Hükümlü Listesine Dön
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Group signature periods with same date range, type, and time
  const groupedPeriods = groupSignaturePeriods(convict.activePeriods || []);

  // Process signature periods for display using the new utility
  const signaturePeriods = groupedPeriods.map((period: SignaturePeriod) => {
    const { frequency, days, time } = formatPeriodDisplay(period);
    // Use actual time range from database if available
    const actualTime = (period.time_start && period.time_end) 
      ? `Saat ${period.time_start} - ${period.time_end}`
      : time;
    
    return {
      startDate: formatDate(period.start_date),
      endDate: formatDate(period.end_date),
      frequency,
      days,
      time: actualTime
    };
  }) || [];

  // Generate signature dates using the new utility
  // Calculate appropriate number of dates based on periods
  const activePeriods = convict.activePeriods || [];
  
  // Calculate total period duration to determine number of pages needed
  let totalDatesNeeded = 24; // Default minimum
  
  if (activePeriods.length > 0) {
    // Estimate dates needed based on frequency
    let estimatedSignatureDays = 0;
    activePeriods.forEach(period => {
      const periodDays = Math.ceil((new Date(period.end_date).getTime() - new Date(period.start_date).getTime()) / (1000 * 60 * 60 * 24));
      
      if (period.frequency_type === 'WEEKLY') {
        const weeklyDays = period.frequency_value.split(',').length;
        estimatedSignatureDays += Math.ceil(periodDays / 7) * weeklyDays;
      } else if (period.frequency_type === 'X_DAYS') {
        const interval = parseInt(period.frequency_value) || 1;
        estimatedSignatureDays += Math.ceil(periodDays / interval);
      } else if (period.frequency_type === 'MONTHLY_SPECIFIC') {
        const monthlyDays = period.frequency_value.split(',').length;
        const months = Math.ceil(periodDays / 30);
        estimatedSignatureDays += months * monthlyDays;
      }
    });
    
    // Use estimated days but ensure minimum of 24 and maximum reasonable limit
    totalDatesNeeded = Math.max(24, Math.min(estimatedSignatureDays, 200));
  }
  
  const generatedDates = generateSignatureDates(activePeriods, totalDatesNeeded, false); // Start from period start, not today
  const allSignatureDates = generatedDates.map(d => d.date);
  
  // Calculate pagination
  const totalPages = Math.ceil(allSignatureDates.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const signatureDates = allSignatureDates.slice(startIndex, endIndex);
  
  // Calculate date range for current page
  const pageStartDate = signatureDates.length > 0 ? signatureDates[0] : '';
  const pageEndDate = signatureDates.length > 0 ? signatureDates[signatureDates.length - 1] : '';

  // Calculate summary statistics (for future use)
  // const periodSummary = convict.activePeriods?.reduce((summary, period) => {
  //   const type = period.frequency_type;
  //   summary[type] = (summary[type] || 0) + 1;
  //   return summary;
  // }, {} as Record<string, number>) || {};

  return (
    <>
      {/* Print/Navigation Controls - Hidden in print */}
      <div className="windows-content print:hidden">
        <div className="windows-toolbar-secondary">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="windows-title">
                İmza Föyü
              </h1>
              <p className="windows-subtitle">
                {convict.first_name} {convict.last_name} - TC: {convict.tc_no}
                {totalPages > 1 && (
                  <span className="ml-4">
                    (Sayfa {currentPage}/{totalPages} - {pageStartDate} / {pageEndDate})
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {/* Pagination Controls - Only show if more than 1 page */}
              {totalPages > 1 && (
                <div className="flex items-center space-x-1 mr-4">
                  <Button
                    variant="windows"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                  </Button>
                  <span className="text-xs px-2 py-1 bg-gray-100 rounded">
                    {currentPage}/{totalPages}
                  </span>
                  <Button
                    variant="windows"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRightIcon className="w-4 h-4" />
                  </Button>
                </div>
              )}
              
              <Button
                variant="windows-primary"
                size="sm"
                onClick={handlePrint}
                disabled={isGeneratingPDF}
              >
                <PrinterIcon className="w-4 h-4 mr-2" />
                {isGeneratingPDF ? 'PDF Oluşturuluyor...' : 'PDF Olarak Kaydet'}
              </Button>
              <Link href="/convicts">
                <Button variant="windows" size="sm">
                  <ArrowLeftIcon className="w-4 h-4 mr-2" />
                  Listeye Dön
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Signature Form Document - Styled for A4 printing */}
      <div
        ref={printContentRef}
        className="signature-form-document print:p-0 print:m-0 print:shadow-none print:border-none p-8"
      >
        <div className="signature-form-page">
          {/* Document Title - Positioned at bottom center */}
          <div className="signature-form-title">
            YÜKÜMLÜ İMZA ÇİZELGESİ
          </div>
          {/* Header Section */}
          <div className="signature-form-header">
            {/* Left Section - Labels */}
            <div className="signature-form-header-section signature-form-header-label">
              <div className="signature-form-header-title">
                Yükümlünün
              </div>
            </div>

            {/* Middle Section - Field Labels */}
            <div className="signature-form-header-section signature-form-header-fields">
              <div className="signature-form-field">
                <div className="signature-form-field-label">Dosya Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Adı Soyadı</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">T.C. Kimlik Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Telefon Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Yakınına Ait Telefon Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">İkamet Adresi</div>
              </div>
            </div>

            {/* Right Section - Field Values */}
            <div className="signature-form-header-section signature-form-header-values">
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.file_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.first_name.toUpperCase()} {convict.last_name.toUpperCase()}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.tc_no}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.phone_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.relative_phone_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">
                  {convict.address ? (
                    convict.address.split('\n').map((line, index) => (
                      <span key={index}>
                        {line}
                        {index < convict.address!.split('\n').length - 1 && <br />}
                      </span>
                    ))
                  ) : (
                    '-'
                  )}
                </div>
              </div>
            </div>

            {/* Signature Schedule Section */}
            <div className="signature-form-header-section signature-form-schedule-label">
              <div className="signature-form-header-title">
                İmza Gün ve Saatleri
              </div>
            </div>

            <div className="signature-form-header-section signature-form-schedule-values">
              <div className="signature-form-schedule">
                {signaturePeriods.map((period, index) => (
                  <div key={index} className="signature-form-schedule-item">
                    <div className="signature-form-schedule-text">
                      {period.startDate} - {period.endDate}
                    </div>
                    <div className="signature-form-schedule-text">
                      {period.frequency}
                    </div>
                    {period.days && (
                      <div className="signature-form-schedule-text">
                        {period.days}
                      </div>
                    )}
                    <div className="signature-form-schedule-text">
                      {period.time}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Signature Sample Section */}
            <div className="signature-form-header-section signature-form-sample">
              <div className="signature-form-sample-box">
                <div className="signature-form-sample-number">1</div>
              </div>
              <div className="signature-form-sample-label">
                YÜKÜMLÜNÜN
                <br />
                İMZA ÖRNEĞİ
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div className="signature-form-table">
            {/* Table Headers */}
            <div className="signature-form-table-headers">
              <div className="signature-form-table-section-labels">
                <div className="signature-form-table-section-label signature-form-table-section-convict">YÜKÜMLÜNÜN</div>
                <div className="signature-form-table-section-label signature-form-table-section-officer">GÖREVLİNİN</div>
              </div>
              <div className="signature-form-table-header-section">
                <div className="signature-form-table-header signature-form-table-header-sn">S.N.</div>
                <div className="signature-form-table-header signature-form-table-header-date">İMZA TARİHİ</div>
                <div className="signature-form-table-header signature-form-table-header-time">İMZA SAATİ</div>
                <div className="signature-form-table-header signature-form-table-header-convict-sig">İMZASI</div>
                <div className="signature-form-table-header signature-form-table-header-officer-name">ADI SOYADI/ÜNVANI</div>
                <div className="signature-form-table-header signature-form-table-header-officer-sig">İMZASI</div>
              </div>
            </div>

            {/* Table Rows */}
            <div className="signature-form-table-rows">
              {signatureDates.map((date, index) => (
                <div key={index} className="signature-form-table-row">
                  <div className="signature-form-table-cell signature-form-table-cell-sn">
                    {index + 1}
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-date">
                    {date}
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-time">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-convict-sig">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-officer-name">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-officer-sig">
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
