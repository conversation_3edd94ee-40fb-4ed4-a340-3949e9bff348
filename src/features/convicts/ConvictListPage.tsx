import { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Badge } from '../../components/ui/badge';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '../../components/ui/alert-dialog';
import { useToast } from '../../hooks/use-toast';
import DataTable from '../../components/common/DataTable';
import ConvictExcelImport from './ConvictExcelImport';
import ConvictExcelExport from './ConvictExcelExport';
import { formatDate } from '../../lib/utils';
import { getConvicts, searchConvictByTcNo, searchConvictsByName, searchConvictsByFileNumber, deleteConvict, bulkDeleteConvicts, updateConvictSequenceNumber } from '../../lib/tauri-api';
import {
  PlusIcon,
  PencilIcon,
  CalendarIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  TrashIcon,
  PrinterIcon,
  ClipboardDocumentIcon,
} from '@heroicons/react/24/outline';
import type { Convict } from '../../shared/schema';
import { useDebounce } from '../../hooks/useDebounce';
import { useDisplaySettings } from '../../hooks/useSettings';

export default function ConvictListPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState<'name' | 'tc' | 'file_number'>('name');
  const [isActive, setIsActive] = useState<boolean | undefined>(undefined);
  const [selectedConvicts, setSelectedConvicts] = useState<Convict[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [editingSequenceId, setEditingSequenceId] = useState<number | null>(null);
  const [editingSequenceValue, setEditingSequenceValue] = useState<string>('');

  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { recordsPerPage } = useDisplaySettings();

  // Get all convicts with refresh capability
  const { 
    data: allConvicts, 
    isLoading: isLoadingAll, 
    refetch: refetchAll,
    isRefetching: isRefreshingAll 
  } = useQuery({
    queryKey: ['convicts'],
    queryFn: getConvicts,
    refetchOnWindowFocus: false,
  });

  // Search by TC No
  const { data: tcSearchResult, isLoading: isSearchingTc } = useQuery({
    queryKey: ['convict-search-tc', debouncedSearchTerm],
    queryFn: () => searchConvictByTcNo(debouncedSearchTerm),
    enabled: searchType === 'tc' && debouncedSearchTerm.length >= 3,
    refetchOnWindowFocus: false,
  });

  // Search by Name
  const { data: nameSearchResults, isLoading: isSearchingName } = useQuery({
    queryKey: ['convict-search-name', debouncedSearchTerm],
    queryFn: () => {
      const names = debouncedSearchTerm.split(' ').filter(Boolean);
      const firstName = names[0] || '';
      const lastName = names[1] || '';
      return searchConvictsByName(firstName, lastName);
    },
    enabled: searchType === 'name' && debouncedSearchTerm.length >= 2,
    refetchOnWindowFocus: false,
  });

  // Search by File Number
  const { data: fileNumberSearchResults, isLoading: isSearchingFileNumber } = useQuery({
    queryKey: ['convict-search-file-number', debouncedSearchTerm],
    queryFn: () => searchConvictsByFileNumber(debouncedSearchTerm),
    enabled: searchType === 'file_number' && debouncedSearchTerm.length >= 1,
    refetchOnWindowFocus: false,
  });

  // Filtered and processed data
  const { filteredConvicts, paginatedConvicts, totalPages } = useMemo(() => {
    let results: Convict[] = [];

    if (debouncedSearchTerm) {
      if (searchType === 'tc' && tcSearchResult) {
        results = [tcSearchResult];
      } else if (searchType === 'name' && nameSearchResults) {
        results = nameSearchResults;
      } else if (searchType === 'file_number' && fileNumberSearchResults) {
        results = fileNumberSearchResults;
      }
    } else {
      results = allConvicts || [];
    }

    // Apply active filter
    if (isActive !== undefined) {
      results = results.filter(convict => convict.is_active === isActive);
    }

    // Calculate pagination
    const totalItems = results.length;
    const totalPages = Math.ceil(totalItems / recordsPerPage);
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const paginatedResults = results.slice(startIndex, endIndex);

    return {
      filteredConvicts: results,
      paginatedConvicts: paginatedResults,
      totalPages,
    };
  }, [allConvicts, tcSearchResult, nameSearchResults, fileNumberSearchResults, debouncedSearchTerm, searchType, isActive, currentPage, recordsPerPage]);

  const isLoading = isLoadingAll || isSearchingTc || isSearchingName || isSearchingFileNumber;
  const isRefreshing = isRefreshingAll;

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: deleteConvict,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['convicts'] });
      queryClient.invalidateQueries({ queryKey: ['convict-search-tc'] });
      queryClient.invalidateQueries({ queryKey: ['convict-search-name'] });
      queryClient.invalidateQueries({ queryKey: ['convict-search-file-number'] });
      toast({
        title: "Başarılı",
        description: "Hükümlü başarıyla silindi.",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Delete convict error:', error);
      toast({
        title: "Hata",
        description: "Hükümlü silinirken bir hata oluştu: " + (error?.message || error?.toString() || error || 'Bilinmeyen hata'),
        variant: "destructive"
      });
    }
  });

  // Update sequence number mutation
  const updateSequenceMutation = useMutation({
    mutationFn: ({ id, sequenceNumber }: { id: number; sequenceNumber: number | null }) =>
      updateConvictSequenceNumber(id, sequenceNumber),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['convicts'] });
      setEditingSequenceId(null);
      setEditingSequenceValue('');
      toast({
        title: "Başarılı",
        description: "Sıra numarası güncellendi.",
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Update sequence number error:', error);
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Sıra numarası güncellenirken bir hata oluştu.",
        variant: "destructive"
      });
    }
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: bulkDeleteConvicts,
    onSuccess: (deletedCount) => {
      queryClient.invalidateQueries({ queryKey: ['convicts'] });
      queryClient.invalidateQueries({ queryKey: ['convict-search-tc'] });
      queryClient.invalidateQueries({ queryKey: ['convict-search-name'] });
      queryClient.invalidateQueries({ queryKey: ['convict-search-file-number'] });
      setSelectedConvicts([]); // Clear selection
      toast({
        title: "Başarılı",
        description: `${deletedCount} hükümlü başarıyla silindi.`,
        variant: "default"
      });
    },
    onError: (error) => {
      console.error('Bulk delete convicts error:', error);
      toast({
        title: "Hata",
        description: "Hükümlüler silinirken bir hata oluştu: " + (error?.message || error?.toString() || error || 'Bilinmeyen hata'),
        variant: "destructive"
      });
    }
  });

  const handleDeleteConvict = (convictId: number) => {
    deleteMutation.mutate(convictId);
  };

  const handleBulkDelete = () => {
    if (selectedConvicts.length === 0) return;
    
    const convictIds = selectedConvicts.map(convict => convict.id);
    bulkDeleteMutation.mutate(convictIds);
  };

  const handleSelectionChange = (newSelectedConvicts: Convict[]) => {
    setSelectedConvicts(newSelectedConvicts);
  };

  const getConvictId = (convict: Convict) => convict.id;

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchTerm('');
    setSearchType('name');
    setIsActive(undefined);
    setSelectedConvicts([]); // Clear selection when clearing filters
    setCurrentPage(1); // Reset to first page
  };

  // Handle refresh
  const handleRefresh = () => {
    refetchAll();
    setSelectedConvicts([]); // Clear selection on refresh
    setCurrentPage(1); // Reset to first page
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedConvicts([]); // Clear selection when changing pages
  };

  // Handle sequence number editing
  const handleSequenceEdit = (convict: Convict) => {
    setEditingSequenceId(convict.id);
    setEditingSequenceValue(convict.sequence_number?.toString() || '');
  };

  const handleSequenceSave = (convictId: number) => {
    const sequenceNumber = editingSequenceValue.trim() === '' ? null : parseInt(editingSequenceValue);
    updateSequenceMutation.mutate({ id: convictId, sequenceNumber });
  };

  const handleSequenceCancel = () => {
    setEditingSequenceId(null);
    setEditingSequenceValue('');
  };

  // Table columns configuration
  const columns = [
    {
      key: 'sequence_number',
      label: 'Sıra No',
      render: (convict: Convict) => {
        if (editingSequenceId === convict.id) {
          return (
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={editingSequenceValue}
                onChange={(e) => setEditingSequenceValue(e.target.value)}
                className="w-16 px-2 py-1 text-xs border rounded"
                placeholder="Sıra"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSequenceSave(convict.id);
                  } else if (e.key === 'Escape') {
                    handleSequenceCancel();
                  }
                }}
              />
              <button
                onClick={() => handleSequenceSave(convict.id)}
                className="text-green-600 hover:text-green-800 text-xs"
                disabled={updateSequenceMutation.isPending}
              >
                ✓
              </button>
              <button
                onClick={handleSequenceCancel}
                className="text-red-600 hover:text-red-800 text-xs"
              >
                ✕
              </button>
            </div>
          );
        }
        return (
          <div
            className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded flex items-center gap-2"
            onClick={() => handleSequenceEdit(convict)}
            title="Düzenlemek için tıklayın"
          >
            <span>{convict.sequence_number || '-'}</span>
            <PencilIcon className="w-3 h-3 text-gray-400" />
          </div>
        );
      },
    },
    {
      key: 'tc_no',
      label: 'TC No',
    },
    {
      key: 'fullName',
      label: 'Ad Soyad',
      render: (convict: Convict) => `${convict.first_name} ${convict.last_name}`,
    },
    {
      key: 'file_number',
      label: 'Dosya No',
      render: (convict: Convict) => convict.file_number || '-',
    },
    {
      key: 'supervisionStartDate',
      label: 'Denetim Başlangıç',
      render: (convict: Convict) => formatDate(convict.supervision_start_date),
    },
    {
      key: 'supervisionEndDate',
      label: 'Denetim Bitiş',
      render: (convict: Convict) => formatDate(convict.supervision_end_date),
    },
    {
      key: 'isActive',
      label: 'Durum',
      render: (convict: Convict) => (
        <Badge 
          variant={convict.is_active ? 'default' : 'secondary'}
          className={`text-xs px-2 py-1 rounded ${
            convict.is_active 
              ? 'bg-green-100 text-green-800 border-green-200' 
              : 'bg-gray-100 text-gray-600 border-gray-200'
          }`}
        >
          {convict.is_active ? 'Aktif' : 'Pasif'}
        </Badge>
      ),
    },
  ];

  // Table actions
  const actions = (convict: Convict) => (
    <div className="flex items-center justify-end space-x-1">
      <Link href={`/convicts/${convict.id}/edit`}>
        <Button 
          variant="windows" 
          size="sm"
          className="h-7 w-7 p-0"
          title="Düzenle"
        >
          <PencilIcon className="w-3 h-3" />
        </Button>
      </Link>
      <Link href={`/convicts/${convict.id}/periods`}>
        <Button 
          variant="windows" 
          size="sm"
          className="h-7 w-7 p-0"
          title="İmza Periyotları"
        >
          <CalendarIcon className="w-3 h-3" />
        </Button>
      </Link>
      <Link href={`/convicts/${convict.id}/signature-form`}>
        <Button 
          variant="windows" 
          size="sm"
          className="h-7 w-7 p-0"
          title="İmza Föyü"
        >
          <PrinterIcon className="w-3 h-3" />
        </Button>
      </Link>
      <Link href={`/convicts/${convict.id}/exemptions`}>
        <Button 
          variant="windows" 
          size="sm"
          className="h-7 w-7 p-0"
          title="Muafiyetler"
        >
          <DocumentTextIcon className="w-3 h-3" />
        </Button>
      </Link>
      <Link href={`/convicts/${convict.id}/violation-record`}>
        <Button 
          variant="windows" 
          size="sm"
          className="h-7 w-7 p-0"
          title="Tutanak"
        >
          <ClipboardDocumentIcon className="w-3 h-3" />
        </Button>
      </Link>
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button 
            variant="windows" 
            size="sm"
            className="h-7 w-7 p-0 hover:bg-red-50 hover:text-red-600"
            title="Sil"
          >
            <TrashIcon className="w-3 h-3" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent className="windows-modal">
          <AlertDialogHeader>
            <AlertDialogTitle className="windows-modal-title">
              Hükümlüyü Sil
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="windows-modal-description">
                <p>
                  <strong>{convict.first_name} {convict.last_name}</strong> (TC: {convict.tc_no}) adlı hükümlüyü silmek istediğinizden emin misiniz?
                </p>
                <p className="mt-2">
                  Bu işlem geri alınamaz ve hükümlüye ait tüm veriler (imza kayıtları, periyotlar, muafiyetler) silinecektir.
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="windows-button">
              İptal
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => handleDeleteConvict(convict.id)}
              disabled={deleteMutation.isPending}
              className="windows-button windows-button-danger"
            >
              {deleteMutation.isPending ? 'Siliniyor...' : 'Sil'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );

  return (
    <div className="windows-content">
      {/* Page Header */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="windows-title">
              Hükümlü Listesi
            </h1>
            <p className="windows-subtitle">
              Toplam {filteredConvicts.length} hükümlü {debouncedSearchTerm && '(filtrelenmiş)'}
              {selectedConvicts.length > 0 && ` • ${selectedConvicts.length} seçili`}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {selectedConvicts.length > 0 && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="border-red-300 text-red-700 hover:bg-red-50"
                    disabled={bulkDeleteMutation.isPending}
                  >
                    <TrashIcon className="w-4 h-4 mr-1" />
                    {selectedConvicts.length} Hükümlüyü Sil
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="windows-modal">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="windows-modal-title">
                      Toplu Silme Onayı
                    </AlertDialogTitle>
                    <AlertDialogDescription asChild>
                      <div className="windows-modal-description">
                        <p>
                          <strong>{selectedConvicts.length} hükümlüyü</strong> silmek istediğinizden emin misiniz?
                        </p>
                        
                        <div className="bg-yellow-50 border border-yellow-200 rounded p-3 mb-3 mt-4">
                          <p className="text-sm text-yellow-800 font-medium">⚠️ Dikkat:</p>
                          <p className="text-sm text-yellow-700 mt-1">
                            Bu işlem geri alınamaz ve seçili hükümlülere ait tüm veriler 
                            (imza kayıtları, periyotlar, muafiyetler) kalıcı olarak silinecektir.
                          </p>
                        </div>
                        
                        <div className="text-sm text-gray-600">
                          <p className="font-medium mb-2">Silinecek hükümlüler:</p>
                          <div className="max-h-32 overflow-y-auto space-y-1">
                            {selectedConvicts.map((convict) => (
                              <div key={convict.id} className="flex items-center text-xs bg-gray-50 p-2 rounded">
                                <span className="font-mono">{convict.tc_no}</span>
                                <span className="mx-2">-</span>
                                <span>{convict.first_name} {convict.last_name}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel className="windows-button">
                      İptal
                    </AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleBulkDelete}
                      disabled={bulkDeleteMutation.isPending}
                      className="windows-button windows-button-danger"
                    >
                      {bulkDeleteMutation.isPending ? 'Siliniyor...' : `${selectedConvicts.length} Hükümlüyü Sil`}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            <Button 
              variant="windows" 
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <ArrowPathIcon className={`w-4 h-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
            <ConvictExcelExport disabled={isLoading || filteredConvicts.length === 0} />
            <ConvictExcelImport onImportComplete={handleRefresh} />
            <Link href="/convicts/new">
              <Button variant="windows-primary" size="sm">
                <PlusIcon className="w-4 h-4 mr-1" />
                Yeni Hükümlü Ekle
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <Card className="windows-card mb-4">
        <CardHeader>
          <CardTitle className="windows-section-title">Arama ve Filtreleme</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="space-y-1">
              <Label className="windows-label">Arama Türü</Label>
              <Select
                value={searchType}
                onValueChange={(value: 'name' | 'tc' | 'file_number') => {
                  setSearchType(value);
                  setSearchTerm('');
                }}
              >
                <SelectTrigger className="windows-select">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Ad Soyad</SelectItem>
                  <SelectItem value="tc">TC Kimlik No</SelectItem>
                  <SelectItem value="file_number">Dosya No</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-1">
              <Label className="windows-label">
                {searchType === 'tc' ? 'TC Kimlik No' : searchType === 'file_number' ? 'Dosya No' : 'Ad Soyad'}
              </Label>
              <Input
                className="windows-input"
                placeholder={
                  searchType === 'tc' 
                    ? 'TC Kimlik No ile ara...' 
                    : searchType === 'file_number'
                    ? 'Dosya numarası ile ara...'
                    : 'Ad ve/veya soyad ile ara...'
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="space-y-1">
              <Label className="windows-label">Durum</Label>
              <Select
                value={isActive?.toString() || 'all'}
                onValueChange={(value) => 
                  setIsActive(value === 'all' ? undefined : value === 'true')
                }
              >
                <SelectTrigger className="windows-select">
                  <SelectValue placeholder="Durum seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tümü</SelectItem>
                  <SelectItem value="true">Aktif</SelectItem>
                  <SelectItem value="false">Pasif</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex items-center justify-between pt-3 border-t border-gray-300">
            <Button 
              variant="windows" 
              size="sm"
              onClick={handleClearFilters}
            >
              <XMarkIcon className="w-4 h-4 mr-1" />
              Filtreleri Temizle
            </Button>
            {debouncedSearchTerm && (
              <div className="flex items-center text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded border">
                <MagnifyingGlassIcon className="w-3 h-3 mr-1" />
                <span>
                  {searchType === 'tc' ? 'TC No' : searchType === 'file_number' ? 'Dosya No' : 'Ad Soyad'}: "{debouncedSearchTerm}"
                </span>
                {isSearchingTc || isSearchingName || isSearchingFileNumber ? (
                  <span className="ml-1 animate-pulse">aranıyor...</span>
                ) : null}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <DataTable
        data={paginatedConvicts}
        columns={columns}
        loading={isLoading}
        actions={actions}
        selectable={true}
        selectedItems={selectedConvicts}
        onSelectionChange={handleSelectionChange}
        getItemId={getConvictId}
        pagination={{
          currentPage,
          totalPages,
          totalItems: filteredConvicts.length,
          itemsPerPage: recordsPerPage,
          onPageChange: handlePageChange,
        }}
        emptyMessage={
          debouncedSearchTerm
            ? "Arama kriterlerinize uygun hükümlü bulunamadı"
            : "Henüz hiç hükümlü eklenmemiş"
        }
      />
    </div>
  );
}
