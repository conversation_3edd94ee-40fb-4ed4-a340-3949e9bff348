import { useLocation, useParams } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { getConvicts, updateConvict } from '@/lib/tauri-api';
import ConvictForm from './ConvictForm';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import type { InsertConvict, ConvictWithDetails } from '@shared/schema';

export default function EditConvictPage() {
  const { id } = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: convicts, isLoading } = useQuery<ConvictWithDetails[]>({
    queryKey: ['convicts'],
    queryFn: async () => {
      const convicts = await getConvicts();
      return convicts.map(convict => ({
        ...convict,
        fullName: `${convict.first_name} ${convict.last_name}`,
        activePeriods: [],
        lastSignature: undefined
      }));
    },
  });

  const convict = convicts?.find(c => c.id === Number(id));

  const updateConvictMutation = useMutation({
    mutationFn: async (data: InsertConvict) => {
      return await updateConvict(Number(id), data);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'Hükümlü bilgileri güncellendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['convicts'] });
      setLocation('/convicts');
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'Hükümlü güncellenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: InsertConvict) => {
    updateConvictMutation.mutate(data);
  };

  const handleCancel = () => {
    setLocation('/convicts');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 font-medium">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!convict) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">!</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Hükümlü Bulunamadı</h2>
          <p className="text-gray-600">Düzenlemek istediğiniz hükümlü kaydı bulunamadı.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 text-white p-8 rounded-xl mx-6 mt-6 mb-6 shadow-lg backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Hükümlü Düzenle: {convict.fullName}
            </h1>
            <p className="text-blue-100 text-lg">
              Hükümlü bilgilerini güncelleyin ve değişiklikleri kaydedin
            </p>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-blue-200">TC Kimlik No</p>
              <p className="text-xl font-semibold">{convict.tc_no}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 pb-6">
        <Card className="backdrop-blur-sm bg-white/70 border-white/20 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl">
            <CardTitle className="text-gray-800 flex items-center">
              <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              Hükümlü Bilgilerini Düzenle
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <ConvictForm
              initialData={{
                sequenceNumber: convict.sequence_number ?? undefined,
                tcNo: convict.tc_no,
                firstName: convict.first_name,
                lastName: convict.last_name,
                phoneNumber: convict.phone_number || '',
                relativePhoneNumber: convict.relative_phone_number || '',
                address: convict.address || '',
                fileNumber: convict.file_number || '',
                supervisionStartDate: convict.supervision_start_date,
                supervisionEndDate: convict.supervision_end_date,
                isActive: convict.is_active,
                notes: convict.notes || '',
              }}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              isLoading={updateConvictMutation.isPending}
              submitLabel="Güncelle"
              isEdit={true}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
