import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  KeyIcon,
  ComputerDesktopIcon,
  MagnifyingGlassIcon,
  DocumentChartBarIcon,
  UserGroupIcon,
  CogIcon,
  EyeIcon,
  FolderIcon
} from '@heroicons/react/24/outline';

const KeyboardShortcutsPage = () => {
  const shortcutCategories = [
    {
      name: 'Navigasyon',
      icon: FolderIcon,
      color: 'blue',
      shortcuts: [
        { keys: ['Ctrl', 'Home'], description: 'Ana sayfaya git' },
        { keys: ['Ctrl', 'N'], description: 'Yeni hükü<PERSON>l<PERSON> ekle' },
        { keys: ['Ctrl', 'L'], description: 'Hükümlü listesini aç' },
        { keys: ['Ctrl', 'S'], description: '<PERSON><PERSON>za kaydı sayfasını aç' },
      ]
    },
    {
      name: '<PERSON><PERSON> ve Filtreleme',
      icon: MagnifyingGlassIcon,
      color: 'green',
      shortcuts: [
        { keys: ['Ctrl', 'F'], description: 'Arama kutusuna odaklan' },
        { keys: ['Ctrl', 'Shift', 'F'], description: 'Filtreleme seçeneklerini aç' },
      ]
    },
    {
      name: 'Sistem',
      icon: ComputerDesktopIcon,
      color: 'purple',
      shortcuts: [
        { keys: ['F1'], description: 'Kullanım kılavuzunu aç' },
        { keys: ['F5'], description: 'Sayfayı yenile' },
        { keys: ['Ctrl', 'R'], description: 'Sayfayı yenile' },
        { keys: ['Ctrl', 'P'], description: 'Sayfayı yazdır' },
        { keys: ['Alt', 'F4'], description: 'Uygulamadan çık' },
      ]
    },
    {
      name: 'Görünüm',
      icon: EyeIcon,
      color: 'orange',
      shortcuts: [
        { keys: ['Ctrl', 'T'], description: 'Araç çubuğunu göster/gizle' },
        { keys: ['Ctrl', 'B'], description: 'Durum çubuğunu göster/gizle' },
      ]
    },
    {
      name: 'Raporlar',
      icon: DocumentChartBarIcon,
      color: 'indigo',
      shortcuts: [
        { keys: ['Ctrl', 'Alt', 'D'], description: 'Günlük raporu aç' },
        { keys: ['Ctrl', 'Alt', 'V'], description: 'İhlal raporu aç' },
      ]
    },
    {
      name: 'Yönetim',
      icon: UserGroupIcon,
      color: 'pink',
      shortcuts: [
        { keys: ['Ctrl', 'Alt', 'U'], description: 'Kullanıcı yönetimi' },
        { keys: ['Ctrl', ','], description: 'Sistem ayarları' },
      ]
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'border-blue-200 text-blue-700 bg-blue-50/80',
      green: 'border-green-200 text-green-700 bg-green-50/80',
      purple: 'border-purple-200 text-purple-700 bg-purple-50/80',
      orange: 'border-orange-200 text-orange-700 bg-orange-50/80',
      indigo: 'border-indigo-200 text-indigo-700 bg-indigo-50/80',
      pink: 'border-pink-200 text-pink-700 bg-pink-50/80'
    };
    return colorMap[color as keyof typeof colorMap] || 'border-gray-200 text-gray-700 bg-gray-50/80';
  };

  const getBadgeColorClasses = (color: string) => {
    const badgeColorMap = {
      blue: 'bg-blue-100 text-blue-700 border-blue-200',
      green: 'bg-green-100 text-green-700 border-green-200',
      purple: 'bg-purple-100 text-purple-700 border-purple-200',
      orange: 'bg-orange-100 text-orange-700 border-orange-200',
      indigo: 'bg-indigo-100 text-indigo-700 border-indigo-200',
      pink: 'bg-pink-100 text-pink-700 border-pink-200'
    };
    return badgeColorMap[color as keyof typeof badgeColorMap] || 'bg-gray-100 text-gray-700 border-gray-200';
  };

  const renderKeyboardKeys = (keys: string[], color: string) => {
    return (
      <div className="flex items-center gap-1">
        {keys.map((key, index) => (
          <div key={index} className="flex items-center">
            <kbd className={`px-2 py-1 text-xs font-mono rounded border ${getBadgeColorClasses(color)} shadow-sm`}>
              {key}
            </kbd>
            {index < keys.length - 1 && (
              <span className="mx-1 text-gray-400 text-sm">+</span>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col p-6 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
          <KeyIcon className="w-8 h-8 text-blue-600" />
          Klavye Kısayolları - EITS
        </h1>
        <p className="text-gray-600">
          Sistem genelinde kullanabileceğiniz klavye kısayolları ve hızlı erişim tuşları
        </p>
      </div>

      <ScrollArea className="flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {shortcutCategories.map((category, index) => (
            <Card key={index} className={`bg-white/80 backdrop-blur-sm shadow-lg ${getColorClasses(category.color)}`}>
              <CardHeader className="pb-3">
                <CardTitle className={`flex items-center gap-2 text-lg ${category.color === 'blue' ? 'text-blue-700' : 
                  category.color === 'green' ? 'text-green-700' :
                  category.color === 'purple' ? 'text-purple-700' :
                  category.color === 'orange' ? 'text-orange-700' :
                  category.color === 'indigo' ? 'text-indigo-700' :
                  category.color === 'pink' ? 'text-pink-700' : 'text-gray-700'
                }`}>
                  <category.icon className="w-5 h-5" />
                  {category.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.shortcuts.map((shortcut, shortcutIndex) => (
                    <div key={shortcutIndex} className="flex items-center justify-between p-3 rounded-lg bg-white/60 hover:bg-white/80 transition-colors border border-white/40">
                      <span className="text-sm text-gray-700 font-medium flex-1 mr-4">
                        {shortcut.description}
                      </span>
                      {renderKeyboardKeys(shortcut.keys, category.color)}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-8">
          <Card className="bg-white/80 backdrop-blur-sm border-gray-200 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-gray-700">
                <CogIcon className="w-5 h-5" />
                Klavye Kısayolları Hakkında
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-800">Kullanım İpuçları</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <span>Klavye kısayolları, hızlı navigasyon için tasarlanmıştır</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <span>Ctrl tuşu ile birlikte kullanılan kısayollar en yaygın olanlarıdır</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <span>F1 tuşu her zaman yardım sayfasını açar</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <span>F5 veya Ctrl+R ile sayfayı yenileyebilirsiniz</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-800">Özel Notlar</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <span>Yönetici kısayolları sadece yönetici hesaplarında çalışır</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <span>Bazı kısayollar sayfa içeriğine bağlı olarak değişebilir</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <span>Arama kısayolları (Ctrl+F) aktif olan arama kutusuna odaklanır</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <span>ESC tuşu açık olan diyalogları kapatır</span>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4">
                <div className="bg-blue-50/80 p-4 rounded-lg border border-blue-200">
                  <h5 className="font-semibold text-blue-800 mb-2">Hatırlatma</h5>
                  <p className="text-sm text-blue-700">
                    Bu klavye kısayolları sisteminizi daha verimli kullanmanıza yardımcı olur. 
                    Günlük iş akışınızda sık kullandığınız işlemler için ilgili kısayolları ezberlemek, 
                    çalışma hızınızı önemli ölçüde artıracaktır.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};

export default KeyboardShortcutsPage;
