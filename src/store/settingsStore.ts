import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { SystemSettings } from '../shared/schema';

interface SettingsState {
  settings: SystemSettings;
  isLoading: boolean;
  setSettings: (settings: Partial<SystemSettings>) => void;
  updateSetting: <K extends keyof SystemSettings>(key: K, value: SystemSettings[K]) => void;
  setLoading: (loading: boolean) => void;
  resetToDefaults: () => void;
}

// Default settings values
export const defaultSettings: SystemSettings = {
  // General Settings
  systemName: 'EİTS - Elektronik İmza Takip Sistemi',
  organizationName: 'Ortaca Polis Merkezi Amirliği',
  systemLanguage: 'tr',
  timeZone: 'Europe/Istanbul',
  dateFormat: 'DD/MM/YYYY',
  
  // Signature Settings
  signatureToleranceMinutes: 15,
  autoViolationDetection: true,
  requireReasonForLateSignature: true,
  maxDailySignatures: 5,
  
  // Session & Security Settings
  sessionTimeoutMinutes: 480,
  passwordMinLength: 6,
  requireStrongPassword: true,
  enableTwoFactorAuth: false,
  maxLoginAttempts: 3,
  lockoutDurationMinutes: 15,
  
  // Notification Settings
  enableEmailNotifications: false,
  emailServer: '',
  emailPort: 587,
  emailUsername: '',
  emailPassword: '',
  enableSystemNotifications: true,
  notificationSound: true,
  
  // Data Management
  autoBackupEnabled: true,
  backupIntervalDays: 7,
  dataRetentionDays: 1095, // 3 years
  
  // Display Settings
  recordsPerPage: 25,
  enableAnimations: true,
  compactMode: false,
  showWelcomeMessage: true,
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      settings: defaultSettings,
      isLoading: false,
      
      setSettings: (newSettings) => 
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        })),
      
      updateSetting: (key, value) =>
        set((state) => ({
          settings: { ...state.settings, [key]: value }
        })),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      resetToDefaults: () => set({ settings: defaultSettings }),
    }),
    {
      name: 'settings-storage',
      partialize: (state) => ({ settings: state.settings }),
    }
  )
);
