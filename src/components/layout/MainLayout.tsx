import { ReactNode, useEffect, useState } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import WindowsMenuBar from './WindowsMenuBar';
import WindowsToolbar from './WindowsToolbar';
import WindowsStatusBar from './WindowsStatusBar';
import WindowsNavigationPanel from './WindowsNavigationPanel';
import WindowControls from './WindowControls';
import useKeyboardShortcuts from '@/hooks/useKeyboardShortcuts';
import { KeyboardShortcutsDialog } from '@/components/common/KeyboardShortcutsDialog';
import { useKeyboardShortcutsDialog } from '@/hooks/useKeyboardShortcutsDialog';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { useUIStore } from '@/store/uiStore';
import { useSettingsStore, defaultSettings } from '@/store/settingsStore';
import { getAllSettings, convertSettingsToSystemSettings } from '@/lib/tauri-api';

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  // Initialize keyboard shortcuts dialog
  const { isOpen, closeDialog } = useKeyboardShortcutsDialog();

  // Get UI visibility states
  const isToolbarVisible = useUIStore((state) => state.isToolbarVisible);
  const isStatusBarVisible = useUIStore((state) => state.isStatusBarVisible);

  // Get settings
  const { settings, setSettings } = useSettingsStore();
  const [isSettingsLoaded, setIsSettingsLoaded] = useState(false);

  // Load settings once on mount
  useEffect(() => {
    if (isSettingsLoaded) return;

    const loadSettings = async () => {
      try {
        const dbSettings = await getAllSettings();
        const systemSettings = convertSettingsToSystemSettings(dbSettings);
        const mergedSettings = { ...defaultSettings, ...systemSettings };
        setSettings(mergedSettings);
        setIsSettingsLoaded(true);
      } catch (error) {
        console.error('Failed to load settings in MainLayout:', error);
        setIsSettingsLoaded(true); // Still mark as loaded to prevent infinite retries
      }
    };

    loadSettings();
  }, [isSettingsLoaded, setSettings]);

  // Update window title when system name changes
  useEffect(() => {
    if (!isSettingsLoaded) return;

    const updateWindowTitle = async () => {
      const window = getCurrentWindow();
      await window.setTitle(settings.systemName);
    };

    updateWindowTitle();
  }, [settings.systemName, isSettingsLoaded]);

  // Handle title bar double click for maximize/restore
  const handleTitleBarDoubleClick = async () => {
    const appWindow = getCurrentWindow();
    await appWindow.toggleMaximize();
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 font-segoe">      {/* Windows 11 Title Bar */}
      <div 
        className="bg-white/20 backdrop-blur-xl border-b border-white/10 px-4 py-2 flex items-center justify-between h-12 shadow-sm cursor-default select-none window-title-bar"
        onDoubleClick={handleTitleBarDoubleClick}
        data-tauri-drag-region
        style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
      >
        <div className="flex items-center space-x-3">
          <CheckCircleIcon className="h-5 w-5 text-blue-600" />
          <span className="text-sm font-medium text-gray-800">{settings.systemName}</span>
        </div>
        <div className="flex items-center" style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}>
          <WindowControls />
        </div>
      </div>
      
      {/* Windows Menu Bar */}
      <WindowsMenuBar />
      
      {/* Windows Toolbar */}
      {isToolbarVisible && <WindowsToolbar />}
      
      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Navigation Panel */}
        <WindowsNavigationPanel />
        
        {/* Content Area */}
        <main className="flex-1 overflow-auto bg-white/80 backdrop-blur-sm border-l border-white/20 shadow-lg">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
      
      {/* Windows Status Bar */}
      {isStatusBarVisible && <WindowsStatusBar />}
      
      {/* Keyboard Shortcuts Dialog */}
      <KeyboardShortcutsDialog isOpen={isOpen} onClose={closeDialog} />
    </div>
  );
}
